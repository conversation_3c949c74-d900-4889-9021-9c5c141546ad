# Meteor 1.21.1 GGB 辅助

## 🎯 已完成功能
- ✅ 自动后退
- ✅ 投影后退
- ✅ 慢速前进
- ✅ 靓仔转圈 (惊呆所有人)
- ✅ 自动挡水晶
- ✅ 种树模块
- ✅ 智能背包管理
- ✅ 无限鞘翅 (智能飞行)

## 📋 TODO 计划
- 🔄 智能Log优化
- 🔄 平台搭建模块
- 🔄 更多防护功能


## ✨ 功能模块介绍

不断添加新的模块中，你可以提出新功能的建议。目前的功能如下：

### **Auto Trash (自动扔垃圾)**
- **功能**: 自动丢弃您库存中指定的物品。
- **特点**:
    - **分组管理**: 您可以设置两组独立的物品列表，方便对不同类型的垃圾进行分类。
    - **自定义延迟**: 可自由调整丢弃物品的频率，防止操作过快。

### **Auto Sand Miner (自动挖沙)**
- **功能**: 智能自动挖沙，集成背包管理和工具维护功能。
- **特点**:
    - **智能挖掘**: 使用 Baritone 自动寻找并挖掘沙子
    - **背包管理**: 背包满时自动前往指定潜影盒存放沙子
    - **工具维护**: 铲子耐久度低时自动更换新工具
    - **自动返回**: 完成存储或换工具后自动返回挖掘位置
    - **可配置设置**: 自定义挖掘范围、延迟、潜影盒位置等

### **HandsomeSpin (靓仔转圈)** 🌟
- **功能**: 让玩家持续旋转，成为全场最靓的仔，惊呆所有人！
- **特点**:
    - **精确旋转**: 每tick可自定义旋转角度（-360°到360°）
    - **智能暂停**: 检测到玩家移动时自动暂停旋转
    - **炫酷特效**: 华丽的启动消息和多样化状态提示
    - **里程碑系统**: 成就提醒
    - **移动检测**: 智能识别按键输入和位置变化
    - **状态管理**: 完整的启动/暂停/恢复逻辑
    - **用户友好**: 丰富的设置选项和可开关的特效模式
    - **用户友好**: 🔥 你就是全场最靓的仔！ 🔥

### **Auto Crystal Block (自动挡水晶)** 🛡️
- **功能**: 智能检测附近的末影水晶并自动放置方块进行防护，保护玩家免受水晶爆炸伤害。
- **特点**:
    - **智能检测**: 实时监控6格范围内的END_CRYSTAL实体(检测范围可调)
    - **自动防护**: 检测到水晶时立即在合适位置放置防护方块
    - **方块优先级**: 智能选择最佳防护方块（黑曜石 > 石头 > 木头等）
    - **灵活放置**: 支持脚边防护模式和拦截模式两种策略

### **InfiniteElytra (无限耐久鞘翅)** 🚀
- **功能**: 智能管理鞘翅使用，通过周期性切换装备从而不消耗耐久度，并自动使用烟花火箭维持飞行。
- **特点**:
    - **耐久保护**: 智能切换鞘翅装备状态，无限延长鞘翅使用寿命
    - **自动飞行**: 检测到滑翔状态时自动启动鞘翅飞行
    - **烟花助推**: 自动使用烟花火箭维持飞行高度和速度
    - **精确控制**: 可自定义鞘翅开启/关闭时长和烟花发射间隔
    - **智能检测**: 自动识别玩家飞行状态，无需手动操作
    - **背包管理**: 自动在背包和装备栏之间移动鞘翅

