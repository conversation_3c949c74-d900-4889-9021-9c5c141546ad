# TreeAura 优化逻辑流程说明

## 优化前后对比

### 优化前的问题
1. **重复扫描**: 每次寻找种植位置时都要重新扫描背包
2. **效率低下**: 先找位置再检查是否有对应物品，导致无效计算
3. **逻辑分散**: 种植逻辑分散在多个方法中，难以维护

### 优化后的改进
1. **缓存机制**: 定期扫描背包并缓存可用物品
2. **智能匹配**: 基于可用物品来寻找对应的种植位置
3. **统一逻辑**: 集中处理种植逻辑，提高代码可维护性

## 新的逻辑流程

### 1. 背包扫描阶段
```java
// 每1秒扫描一次背包
private void scanInventoryForPlantItems() {
    availablePlantItems.clear();
    
    // 扫描所有槽位
    for (int i = 0; i < 45; i++) {
        ItemStack stack = player.getInventory().getStack(i);
        if (stack.getItem() instanceof BlockItem) {
            Block block = Block.getBlockFromItem(stack.getItem());
            
            // 检查是否为可种植物品
            if (block instanceof SaplingBlock || 
                block.equals(Blocks.CRIMSON_FUNGUS) || 
                block.equals(Blocks.WARPED_FUNGUS)) {
                
                availablePlantItems.put(block, slot);
            }
        }
    }
}
```

### 2. 位置匹配阶段
```java
// 基于可用物品寻找种植位置
private BlockPos findPlantLocation() {
    if (availablePlantItems.isEmpty()) return null;
    
    // 遍历附近位置
    for (BlockPos pos : nearbyPositions) {
        Block groundBlock = world.getBlockState(pos).getBlock();
        
        // 检查是否有对应的种植物品
        if (canPlantAtPosition(pos, groundBlock)) {
            validPositions.add(pos);
        }
    }
    
    // 应用防止堵路过滤
    // 距离排序
    // 返回最佳位置
}
```

### 3. 智能种植阶段
```java
// 使用缓存的物品信息进行种植
private void doPlant(BlockPos plantPos) {
    Block groundBlock = world.getBlockState(plantPos).getBlock();
    Integer itemSlot = null;
    
    // 根据地面类型选择合适的物品
    if (groundBlock.equals(Blocks.CRIMSON_NYLIUM)) {
        itemSlot = availablePlantItems.get(Blocks.CRIMSON_FUNGUS);
    } else if (groundBlock.equals(Blocks.WARPED_NYLIUM)) {
        itemSlot = availablePlantItems.get(Blocks.WARPED_FUNGUS);
    } else {
        // 选择任意可用的树苗
        for (Block block : availablePlantItems.keySet()) {
            if (block instanceof SaplingBlock) {
                itemSlot = availablePlantItems.get(block);
                break;
            }
        }
    }
    
    // 执行种植
    BagUtil.doSwap(itemSlot);
    BaritoneUtil.clickBlock(plantPos, Direction.UP, true, Hand.MAIN_HAND, BaritoneUtil.SwingSide.All);
    BagUtil.doSwap(itemSlot);
}
```

## 核心数据结构

### 物品缓存
```java
// 缓存背包中可用的种植物品
private Map<Block, Integer> availablePlantItems; // Block -> 槽位

// 扫描间隔控制
private long lastInventoryScanTime = 0;
private static final long INVENTORY_SCAN_INTERVAL = 1000; // 1秒
```

### 移动方向记忆
```java
// 记住最后的移动方向
private Direction lastMovementDirection;

// 获取移动方向（包括记忆功能）
private Direction getPlayerMovementDirection() {
    Direction currentDirection = getCurrentMovementDirection();
    
    if (currentDirection != null) {
        lastMovementDirection = currentDirection;
        return currentDirection;
    }
    
    return lastMovementDirection; // 返回记住的方向
}
```

## 性能优化

### 1. 减少重复计算
- **背包扫描**: 从每tick扫描改为每秒扫描
- **位置检查**: 只检查有对应物品的位置
- **缓存结果**: 避免重复的物品查找

### 2. 智能过滤
- **物品匹配**: 菌岩只考虑对应菌类，土壤只考虑树苗
- **防止堵路**: 基于记忆的移动方向进行过滤
- **间隔检查**: 统一的基础检查逻辑

### 3. 错误处理
- **物品缺失**: 明确的错误提示
- **匹配失败**: 详细的失败原因
- **调试信息**: 可选的详细日志

## 使用体验改进

### 1. 更智能的种植
- 自动识别背包中的所有可种植物品
- 根据地面类型自动选择合适的物品
- 避免无效的种植尝试

### 2. 更好的防堵路
- 记住移动方向，即使停止移动也不会堵路
- 更精确的方向判断
- 支持所有移动方式（WASD键、位置变化）

### 3. 更清晰的反馈
- 扫描到物品时的提示
- 种植失败时的具体原因
- 菌类匹配的状态信息

## 兼容性保证

- 完全向后兼容所有现有设置
- 保持原有的骨粉催熟功能
- 不影响其他模块的使用
- 支持所有原有的种植物品类型
