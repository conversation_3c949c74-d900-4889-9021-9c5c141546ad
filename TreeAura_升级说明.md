# TreeAura 模块升级说明

## 新增功能

### 1. 防止堵路功能增强
- **记忆移动方向**: 即使玩家停止移动，模块也会记住最后的移动方向
- **智能种植位置**: 不会在玩家移动方向的前方种植树木，避免堵住玩家的路
- **设置选项**: 新增"防止堵路"开关，可以启用/禁用此功能

### 2. 下界菌类支持
- **支持菌类种植**: 现在支持种植绯红菌（Crimson Fungus）和诡异菌（Warped Fungus）
- **菌岩匹配**: 自动检测对应的菌岩（绯红菌岩/诡异菌岩）
- **智能种植**: 只在对应的菌岩上种植匹配的菌类
- **骨粉催熟**: 支持对菌类使用骨粉进行催熟

## 技术实现

### 移动方向记忆
```java
// 新增字段
private Direction lastMovementDirection;

// 获取移动方向（包括记忆功能）
private Direction getPlayerMovementDirection() {
    Direction currentDirection = getCurrentMovementDirection();
    
    // 如果当前有移动方向，更新记住的方向
    if (currentDirection != null) {
        lastMovementDirection = currentDirection;
        return currentDirection;
    }
    
    // 如果当前没有移动但之前有记住的方向，返回记住的方向
    return lastMovementDirection;
}
```

### 菌类支持
```java
// 菌类检测
private boolean isFungus(Block block) {
    return block.equals(Blocks.CRIMSON_FUNGUS) || block.equals(Blocks.WARPED_FUNGUS);
}

// 菌类和菌岩匹配检查
private boolean isFungusNyliumMatch(Block fungus, Block nylium) {
    if (fungus.equals(Blocks.CRIMSON_FUNGUS) && nylium.equals(Blocks.CRIMSON_NYLIUM)) {
        return true;
    }
    if (fungus.equals(Blocks.WARPED_FUNGUS) && nylium.equals(Blocks.WARPED_NYLIUM)) {
        return true;
    }
    return false;
}
```

### 种植位置判断
```java
// 修改后的种植位置检查
private boolean canPlant(BlockPos pos) {
    Block b = MinecraftClient.getInstance().world.getBlockState(pos).getBlock();
    
    // 检查是否可以种植普通树苗
    boolean canPlantSapling = b.equals(Blocks.GRASS_BLOCK) || /* 其他土壤类型 */;
    
    // 检查是否可以种植菌类（需要对应的菌岩）
    boolean canPlantFungus = b.equals(Blocks.CRIMSON_NYLIUM) || b.equals(Blocks.WARPED_NYLIUM);
    
    return canPlantSapling || canPlantFungus;
}
```

## 使用说明

### 防止堵路功能
1. 在TreeAura设置中启用"防止堵路"选项
2. 开始移动，模块会记住你的移动方向
3. 即使停止移动，模块也不会在你前进方向的前方种植树木
4. 只会在你的身后和侧面种植，确保不会阻挡你的路径

### 下界菌类种植
1. 准备绯红菌或诡异菌放在快捷栏中
2. 确保有对应的菌岩（绯红菌岩配绯红菌，诡异菌岩配诡异菌）
3. 启用TreeAura模块，它会自动在合适的菌岩上种植对应的菌类
4. 准备骨粉可以自动催熟菌类成为巨型菌类

## 注意事项

1. **菌类匹配**: 确保菌类和菌岩类型匹配，否则无法生长
2. **移动记忆**: 模块会记住最后的移动方向，重新激活模块会重置记忆
3. **间隔设置**: 菌类也遵循树苗间隔设置，避免种植过密
4. **骨粉使用**: 菌类和树苗使用相同的骨粉设置和逻辑

## 兼容性

- 完全向后兼容原有的树苗种植功能
- 新功能可以独立启用/禁用
- 不影响现有的设置和配置
