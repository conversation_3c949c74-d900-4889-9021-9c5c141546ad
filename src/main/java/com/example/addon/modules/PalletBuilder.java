package com.example.addon.modules;

import io.netty.handler.codec.socksx.v4.Socks4ClientEncoder$ConstantPool;
import io.netty.handler.codec.socksx.v4.Socks4CommandRequest$ConstantPool;
import java.util.ArrayList;
import java.util.List;
import javassist.bytecode.CodeIterator$LdcW$ConstantPool;
import javassist.bytecode.CodeIterator$Switcher$ConstantPool;
import javassist.bytecode.StackMapTable$Shifter$ConstantPool;
import javassist.bytecode.analysis.Executor$ConstantPool;
import javassist.compiler.ast.Symbol$ConstantPool;
import javax.annotation.concurrent.GuardedBy$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent$ConstantPool;
import meteordevelopment.meteorclient.events.world.ParticleEvent$AddParticle$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorCheckbox$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.containers.WHorizontalList$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.containers.WVerticalList$ConstantPool;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.renderer.text.FontInfo$Type$ConstantPool;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.ColorSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.EnumSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.PacketListSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.StringListSetting$ConstantPool;
import meteordevelopment.meteorclient.systems.config.Config$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.HudGroup$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.combat.AutoCity$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.NoFall$PlaceMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.ItemPhysics$ModelInfo$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Tracers$TracerStyle$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.marker.CuboidMarker$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.VeinMiner$ListMode$ConstantPool;
import meteordevelopment.meteorclient.utils.entity.EntityUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.InventorySorter$MySlot$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.usercheck.CheckUtils;
import meteordevelopment.meteorclient.utils.world.BlockUtilGrim;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.meteorclient.utils.world.Timer;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.block.SlabBlock;
import net.minecraft.block.enums.SlabType;
import net.minecraft.item.Items;
import net.minecraft.state.property.Properties;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.MathHelper;
import org.reflections.vfs.ZipFile$ConstantPool;

public class PalletBuilder extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final SettingGroup renderGeneral = this.settings.createGroup(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(HudGroup$ConstantPool.const_WoD1kV2nyTNaSoI))));
   private final Setting<Boolean> moveStop = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(NoFall$PlaceMode$ConstantPool.const_ltwAdKxvO0ZlTfW))))
            .description(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(EntityUtils$ConstantPool.const_JeTYnpFnVQYJu8A))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Boolean> onlyBottom = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(Render3DEvent$ConstantPool.const_2ELnV1bM4R1fwT8))))
            .description(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(AutoCity$ConstantPool.const_BkdOQvMUaOl9Bac))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Double> range = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(Config$ConstantPool.const_E49L2zJnNBadTle))))
            .description(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(Executor$ConstantPool.const_3YrardDnigqGkqE))))
            .defaultValue(4.0)
            .range(0.0, 6.0)
            .build()
      );
   public final Setting<BlockType> blockType = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(CodeIterator$Switcher$ConstantPool.const_9dgTS7mSdL7sDNi)))))
                  .description(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(StringListSetting$ConstantPool.const_CrN196XeRGngs2n)))))
               .defaultValue(BlockType.STONE))
            .build()
      );
   private final Setting<Integer> blocksPer = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(WVerticalList$ConstantPool.const_HrUk4XgWblU6ToQ))))
            .description(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(ZipFile$ConstantPool.const_pngwdPOnignNyn9))))
            .defaultValue(1)
            .sliderRange(1, 6)
            .build()
      );
   private final Setting<Integer> delay = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(WHorizontalList$ConstantPool.const_ydkVFI2CYZAyo4e))))
            .description(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(GuardedBy$ConstantPool.const_wmwPEoHQ7y4RiZ4))))
            .defaultValue(0)
            .sliderRange(0, 10)
            .build()
      );
   private final Setting<Boolean> render = this.renderGeneral
      .add(
         new BoolSetting.Builder()
            .name(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(ParticleEvent$AddParticle$ConstantPool.const_ZQLlSGt0zmSWBfv))))
            .description(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(VeinMiner$ListMode$ConstantPool.const_ocsP1NL49Q5UkMp))))
            .defaultValue(true)
            .build()
      );
   private final Setting<ShapeMode> shapeMode = this.renderGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(WMeteorCheckbox$ConstantPool.const_f6ievAjGqIp9ZqZ)))))
                  .description(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(CuboidMarker$Mode$ConstantPool.const_g7oLKh4t5TAypQf)))))
               .defaultValue(ShapeMode.Both))
            .build()
      );
   private final Setting<SettingColor> readySideColor = this.renderGeneral
      .add(
         new ColorSetting.Builder()
            .name(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(StackMapTable$Shifter$ConstantPool.const_InYtBQaBWw68Vl2))))
            .description(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(Socks4CommandRequest$ConstantPool.const_EiKJsITOvWFeo7l))))
            .defaultValue(new SettingColor(255, 255, 255, 50))
            .build()
      );
   private final Setting<SettingColor> readyLineColor = this.renderGeneral
      .add(
         new ColorSetting.Builder()
            .name(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(FontInfo$Type$ConstantPool.const_FSxvnm6dMcWFGq9))))
            .description(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(Tracers$TracerStyle$ConstantPool.const_VJgwWsA16A21wId))))
            .defaultValue(new SettingColor(255, 255, 255, 128))
            .build()
      );
   private List<BlockPos> placeList = new ArrayList<>();
   private final Timer timer = new Timer();
   int progress = 0;

   public PalletBuilder() {
      super(
         Categories.Ggboy,
         X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(CodeIterator$LdcW$ConstantPool.const_r2WNgO4izu6Ov41))),
         new StringBuilder(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(Symbol$ConstantPool.const_Syj6T6R5LlD2kfX)))),
         X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(ItemPhysics$ModelInfo$ConstantPool.const_OLD47P0YO9Izaog)))
      );
   }

   @Override
   public void onActivate() {
      if (!CheckUtils.check()) {
         for (int i = 0; i < 3; i++) {
            this.info(Text.of(X112A5zIfn(1fyI2Jcyrq(6iCn8ttNJk(Socks4ClientEncoder$ConstantPool.const_CqgY2kltoQ5nJTf)))));
         }

         if (this.isActive()) {
            this.toggle();
         }
      }
   }

   @Override
   public void init() {
      if (this.isActive()) {
         this.toggle();
      }
   }

   @EventHandler
   public void onTick(TickEvent.Pre event) {
      if (!this.moveStop.get()
         || !mc.options.backKey.isPressed()
            && !mc.options.forwardKey.isPressed()
            && !mc.options.leftKey.isPressed()
            && !mc.options.rightKey.isPressed()) {
         this.placeList.clear();
         this.progress = 0;
         if (this.timer.passedMs(this.delay.get().intValue() * 100)) {
            this.timer.reset();

            for (BlockPos pos : BlockUtils.getSphere(Float.parseFloat(this.range.get().toString()))) {
               if (this.blockType.get() == BlockType.DOWNSLAP) {
                  Direction direction = BlockUtilGrim.getInteractDirection(pos, true);
                  if (BlockUtils.canSee_alien(pos, direction)
                     && mc.world.getBlockState(pos).getBlock().asItem() == Items.AIR
                     && this.neighbor(pos)
                     && this.onlyBottom.get()
                     && pos.getY() == mc.player.getBlockPos().getY()) {
                     this.tryPlace(pos);
                  }
               } else if (BlockUtils.canPlace_alien(pos, this.range.get(), true)
                  && this.neighbor(pos)
                  && this.onlyBottom.get()
                  && pos.getY() == mc.player.getBlockPos().down().getY()
                  && this.blockType.get() != BlockType.DOWNSLAP
                  && mc.world.getBlockState(pos).getBlock().asItem() == Items.AIR) {
                  this.tryPlace(pos);
               }
            }
         }
      }
   }

   @EventHandler
   private void onRender(Render3DEvent event) {
      if (this.render.get()
         && this.placeList.size() > 0
         && (
            InvUtils.find(this.getBlockBytype(this.blockType.get()).asItem()).found()
               || (this.blockType.get() == BlockType.DOWNSLAP || this.blockType.get() == BlockType.UPSLAP)
                  && InvUtils.findClassInventorySlotGrim(SlabBlock.class) != -1
         )) {
         for (int i = 0; i < this.placeList.size(); i++) {
            double x1 = this.placeList.get(i).getX();
            double y1 = this.placeList.get(i).getY();
            double z1 = this.placeList.get(i).getZ();
            double x2 = this.placeList.get(i).getX() + 1;
            double y2 = this.placeList.get(i).getY() + 1;
            double z2 = this.placeList.get(i).getZ() + 1;
            event.renderer.box(x1, y1, z1, x2, y2, z2, this.readySideColor.get(), this.readyLineColor.get(), this.shapeMode.get(), 0);
         }
      }
   }

   private void tryPlace(BlockPos pos) {
      if (pos != null) {
         if (!this.placeList.contains(pos)) {
            if (this.progress < this.blocksPer.get()) {
               if (!(MathHelper.sqrt((float)BlockUtils.getEyesPos().squaredDistanceTo(pos.toCenterPos())) > this.range.get())) {
                  int itemResult = -1;
                  if (this.blockType.get() != BlockType.DOWNSLAP && this.blockType.get() != BlockType.UPSLAP) {
                     itemResult = InvUtils.findItemInventorySlotGrim(this.getBlockBytype(this.blockType.get()).asItem());
                  } else {
                     itemResult = InvUtils.findClassInventorySlotGrim(SlabBlock.class);
                  }

                  if (itemResult != -1) {
                     this.placeList.add(pos);
                     InvUtils.doSwap(itemResult);
                     if (this.blockType.get() == BlockType.UPSLAP) {
                        BlockUtilGrim.placeUpBlock(pos, true, true, true);
                     } else if (this.blockType.get() == BlockType.DOWNSLAP) {
                        BlockUtilGrim.placeDownBlock(pos, true, true, true);
                     } else {
                        BlockUtilGrim.placeBlock(pos, true, true, true);
                     }

                     InvUtils.doSwap(itemResult);
                     InvUtils.sync();
                     this.timer.reset();
                     this.progress++;
                  }
               }
            }
         }
      }
   }

   private boolean neighbor(BlockPos pos) {
      for (Direction direction : Direction.values()) {
         if (direction != Direction.DOWN && direction != Direction.UP) {
            if (this.blockType.get() == BlockType.UPSLAP
               && mc.world.getBlockState(pos.offset(direction)).getBlock() instanceof SlabBlock block
               && mc.world.getBlockState(pos.offset(direction)).get(Properties.SLAB_TYPE) == SlabType.TOP) {
               return true;
            }

            if (this.blockType.get() == BlockType.DOWNSLAP
               && mc.world.getBlockState(pos.offset(direction)).getBlock() instanceof SlabBlock block
               && (
                  mc.world.getBlockState(pos.offset(direction)).get(Properties.SLAB_TYPE) == SlabType.BOTTOM
                     || mc.world.getBlockState(pos.offset(direction)).get(Properties.SLAB_TYPE) == SlabType.DOUBLE
               )) {
               return true;
            }

            if (mc.world.getBlockState(pos.offset(direction)).getBlock() == this.getBlockBytype(this.blockType.get())
               && this.blockType.get() != BlockType.DOWNSLAP
               && this.blockType.get() != BlockType.UPSLAP) {
               return true;
            }
         }
      }

      return false;
   }

   public Block getBlockBytype(BlockType blockType) {
      switch (blockType) {
         case STONE:
            return Blocks.STONE;
         case DIRT:
            return Blocks.DIRT;
         case COBBLESTONE:
            return Blocks.COBBLESTONE;
         case OBSIDIAN:
            return Blocks.OBSIDIAN;
         case STONE_BRICKS:
            return Blocks.STONE_BRICKS;
         case SMOOTHSTON:
            return Blocks.SMOOTH_STONE;
         default:
            return Blocks.STONE;
      }
   }
}
