package com.example.addon.modules;

import io.netty.handler.codec.socksx.v4.Socks4ClientEncoder$ConstantPool;
import io.netty.handler.codec.socksx.v4.Socks4CommandRequest$ConstantPool;
import java.util.ArrayList;
import java.util.List;
import javassist.bytecode.CodeIterator$LdcW$ConstantPool;
import javassist.bytecode.CodeIterator$Switcher$ConstantPool;
import javassist.bytecode.StackMapTable$Shifter$ConstantPool;
import javassist.bytecode.analysis.Executor$ConstantPool;
import javassist.compiler.ast.Symbol$ConstantPool;
import javax.annotation.concurrent.GuardedBy$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent$ConstantPool;
import meteordevelopment.meteorclient.events.world.ParticleEvent$AddParticle$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorCheckbox$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.containers.WHorizontalList$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.containers.WVerticalList$ConstantPool;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.renderer.text.FontInfo$Type$ConstantPool;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.ColorSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.EnumSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.PacketListSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.StringListSetting$ConstantPool;
import meteordevelopment.meteorclient.systems.config.Config$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.HudGroup$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.combat.AutoCity$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.NoFall$PlaceMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.ItemPhysics$ModelInfo$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Tracers$TracerStyle$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.marker.CuboidMarker$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.VeinMiner$ListMode$ConstantPool;
import meteordevelopment.meteorclient.utils.entity.EntityUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.InventorySorter$MySlot$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.usercheck.CheckUtils;
import meteordevelopment.meteorclient.utils.world.BlockUtilGrim;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.meteorclient.utils.world.Timer;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.block.SlabBlock;
import net.minecraft.block.enums.SlabType;
import net.minecraft.item.Items;
import net.minecraft.state.property.Properties;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.MathHelper;
import org.reflections.vfs.ZipFile$ConstantPool;

public class PalletBuilder extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final SettingGroup renderGeneral = this.settings.createGroup("渲染");
   private final Setting<Boolean> moveStop = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name("移动时暂停")
            .description("当玩家移动时暂停自动建造")
            .defaultValue(false)
            .build()
      );
   private final Setting<Boolean> onlyBottom = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name("仅底层建造")
            .description("只在玩家脚下的高度建造方块")
            .defaultValue(true)
            .build()
      );
   private final Setting<Double> range = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name("建造范围")
            .description("自动建造的最大范围")
            .defaultValue(4.0)
            .range(0.0, 6.0)
            .build()
      );
   public final Setting<BlockType> blockType = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name("方块类型"))
                  .description("选择用于建造的方块类型"))
               .defaultValue(BlockType.STONE))
            .build()
      );
   private final Setting<Integer> blocksPer = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name("每次放置数量")
            .description("每个游戏刻放置的方块数量")
            .defaultValue(1)
            .sliderRange(1, 6)
            .build()
      );
   private final Setting<Integer> delay = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name("放置延迟")
            .description("放置方块之间的延迟时间（单位：100毫秒）")
            .defaultValue(0)
            .sliderRange(0, 10)
            .build()
      );
   private final Setting<Boolean> render = this.renderGeneral
      .add(
         new BoolSetting.Builder()
            .name("显示渲染")
            .description("是否显示待放置方块的渲染预览")
            .defaultValue(true)
            .build()
      );
   private final Setting<ShapeMode> shapeMode = this.renderGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name("渲染模式"))
                  .description("选择渲染的显示模式"))
               .defaultValue(ShapeMode.Both))
            .build()
      );
   private final Setting<SettingColor> readySideColor = this.renderGeneral
      .add(
         new ColorSetting.Builder()
            .name("侧面颜色")
            .description("待放置方块渲染的侧面填充颜色")
            .defaultValue(new SettingColor(255, 255, 255, 50))
            .build()
      );
   private final Setting<SettingColor> readyLineColor = this.renderGeneral
      .add(
         new ColorSetting.Builder()
            .name("线条颜色")
            .description("待放置方块渲染的边框线条颜色")
            .defaultValue(new SettingColor(255, 255, 255, 128))
            .build()
      );
   private List<BlockPos> placeList = new ArrayList<>();
   private final Timer timer = new Timer();
   int progress = 0;

   public PalletBuilder() {
      super(
         Categories.Ggboy,
         "托盘建造器",
         "自动在玩家周围建造方块平台或托盘"
      );
   }

   @Override
   public void onActivate() {
      if (!CheckUtils.check()) {
         for (int i = 0; i < 3; i++) {
            this.info(Text.of("检查失败，模块已禁用"));
         }

         if (this.isActive()) {
            this.toggle();
         }
      }
   }

   @Override
   public void init() {
      if (this.isActive()) {
         this.toggle();
      }
   }

   @EventHandler
   public void onTick(TickEvent.Pre event) {
      if (!this.moveStop.get()
         || !mc.options.backKey.isPressed()
            && !mc.options.forwardKey.isPressed()
            && !mc.options.leftKey.isPressed()
            && !mc.options.rightKey.isPressed()) {
         this.placeList.clear();
         this.progress = 0;
         if (this.timer.passedMs(this.delay.get().intValue() * 100)) {
            this.timer.reset();

            for (BlockPos pos : BlockUtils.getSphere(Float.parseFloat(this.range.get().toString()))) {
               if (this.blockType.get() == BlockType.DOWNSLAP) {
                  Direction direction = BlockUtilGrim.getInteractDirection(pos, true);
                  if (BlockUtils.canSee_alien(pos, direction)
                     && mc.world.getBlockState(pos).getBlock().asItem() == Items.AIR
                     && this.neighbor(pos)
                     && this.onlyBottom.get()
                     && pos.getY() == mc.player.getBlockPos().getY()) {
                     this.tryPlace(pos);
                  }
               } else if (BlockUtils.canPlace_alien(pos, this.range.get(), true)
                  && this.neighbor(pos)
                  && this.onlyBottom.get()
                  && pos.getY() == mc.player.getBlockPos().down().getY()
                  && this.blockType.get() != BlockType.DOWNSLAP
                  && mc.world.getBlockState(pos).getBlock().asItem() == Items.AIR) {
                  this.tryPlace(pos);
               }
            }
         }
      }
   }

   @EventHandler
   private void onRender(Render3DEvent event) {
      if (this.render.get()
         && this.placeList.size() > 0
         && (
            InvUtils.find(this.getBlockBytype(this.blockType.get()).asItem()).found()
               || (this.blockType.get() == BlockType.DOWNSLAP || this.blockType.get() == BlockType.UPSLAP)
                  && InvUtils.findClassInventorySlotGrim(SlabBlock.class) != -1
         )) {
         for (int i = 0; i < this.placeList.size(); i++) {
            double x1 = this.placeList.get(i).getX();
            double y1 = this.placeList.get(i).getY();
            double z1 = this.placeList.get(i).getZ();
            double x2 = this.placeList.get(i).getX() + 1;
            double y2 = this.placeList.get(i).getY() + 1;
            double z2 = this.placeList.get(i).getZ() + 1;
            event.renderer.box(x1, y1, z1, x2, y2, z2, this.readySideColor.get(), this.readyLineColor.get(), this.shapeMode.get(), 0);
         }
      }
   }

   private void tryPlace(BlockPos pos) {
      if (pos != null) {
         if (!this.placeList.contains(pos)) {
            if (this.progress < this.blocksPer.get()) {
               if (!(MathHelper.sqrt((float)BlockUtils.getEyesPos().squaredDistanceTo(pos.toCenterPos())) > this.range.get())) {
                  int itemResult = -1;
                  if (this.blockType.get() != BlockType.DOWNSLAP && this.blockType.get() != BlockType.UPSLAP) {
                     itemResult = InvUtils.findItemInventorySlotGrim(this.getBlockBytype(this.blockType.get()).asItem());
                  } else {
                     itemResult = InvUtils.findClassInventorySlotGrim(SlabBlock.class);
                  }

                  if (itemResult != -1) {
                     this.placeList.add(pos);
                     InvUtils.doSwap(itemResult);
                     if (this.blockType.get() == BlockType.UPSLAP) {
                        BlockUtilGrim.placeUpBlock(pos, true, true, true);
                     } else if (this.blockType.get() == BlockType.DOWNSLAP) {
                        BlockUtilGrim.placeDownBlock(pos, true, true, true);
                     } else {
                        BlockUtilGrim.placeBlock(pos, true, true, true);
                     }

                     InvUtils.doSwap(itemResult);
                     InvUtils.sync();
                     this.timer.reset();
                     this.progress++;
                  }
               }
            }
         }
      }
   }

   private boolean neighbor(BlockPos pos) {
      for (Direction direction : Direction.values()) {
         if (direction != Direction.DOWN && direction != Direction.UP) {
            if (this.blockType.get() == BlockType.UPSLAP
               && mc.world.getBlockState(pos.offset(direction)).getBlock() instanceof SlabBlock block
               && mc.world.getBlockState(pos.offset(direction)).get(Properties.SLAB_TYPE) == SlabType.TOP) {
               return true;
            }

            if (this.blockType.get() == BlockType.DOWNSLAP
               && mc.world.getBlockState(pos.offset(direction)).getBlock() instanceof SlabBlock block
               && (
                  mc.world.getBlockState(pos.offset(direction)).get(Properties.SLAB_TYPE) == SlabType.BOTTOM
                     || mc.world.getBlockState(pos.offset(direction)).get(Properties.SLAB_TYPE) == SlabType.DOUBLE
               )) {
               return true;
            }

            if (mc.world.getBlockState(pos.offset(direction)).getBlock() == this.getBlockBytype(this.blockType.get())
               && this.blockType.get() != BlockType.DOWNSLAP
               && this.blockType.get() != BlockType.UPSLAP) {
               return true;
            }
         }
      }

      return false;
   }

   public Block getBlockBytype(BlockType blockType) {
      switch (blockType) {
         case STONE:
            return Blocks.STONE;
         case DIRT:
            return Blocks.DIRT;
         case COBBLESTONE:
            return Blocks.COBBLESTONE;
         case OBSIDIAN:
            return Blocks.OBSIDIAN;
         case STONE_BRICKS:
            return Blocks.STONE_BRICKS;
         case SMOOTHSTON:
            return Blocks.SMOOTH_STONE;
         default:
            return Blocks.STONE;
      }
   }
    enum BlockType {
        DIRT,
        STONE,
        COBBLESTONE,
        OBSIDIAN,
        STONE_BRICKS,
        SMOOTHSTON,
        UPSLAP,
        DOWNSLAP;
    }

}
