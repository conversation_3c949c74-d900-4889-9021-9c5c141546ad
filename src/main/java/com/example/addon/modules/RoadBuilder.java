package com.example.addon.modules;

import com.example.addon.BaseModule;
import com.example.addon.utils.BagUtil;
import com.example.addon.utils.BaritoneUtil;
import com.example.addon.utils.WorldUtils;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.block.SlabBlock;
import net.minecraft.block.enums.SlabType;
import net.minecraft.item.Items;
import net.minecraft.state.property.Properties;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.MathHelper;

import java.util.ArrayList;
import java.util.List;

public class RoadBuilder extends BaseModule {
    private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
    private final SettingGroup renderGeneral = this.settings.createGroup("渲染");
    private final Setting<Boolean> moveStop = this.sgGeneral
        .add(
            new BoolSetting.Builder()
                .name("移动时暂停")
                .description("当玩家移动时暂停自动建造")
                .defaultValue(false)
                .build()
        );
    private final Setting<Boolean> onlyBottom = this.sgGeneral
        .add(
            new BoolSetting.Builder()
                .name("仅底层建造")
                .description("只在玩家脚下的高度建造方块")
                .defaultValue(true)
                .build()
        );
    private final Setting<Double> range = this.sgGeneral
        .add(
            new DoubleSetting.Builder()
                .name("建造范围")
                .description("自动建造的最大范围")
                .defaultValue(4.0)
                .range(0.0, 6.0)
                .build()
        );
    public final Setting<Block> blockType = this.sgGeneral
        .add(new BlockSetting.Builder()
            .name("方块")
            .description("选择用于建造的方块")
            .defaultValue(Blocks.COBBLESTONE)
            .build()
        );

    private final Setting<SlapType> slapType = sgGeneral.add(new EnumSetting.Builder<SlapType>()
        .name("半砖方向(如果有)")
        .description("半砖方向")
        .defaultValue(SlapType.UP)
        .visible(() -> this.blockType.get() instanceof SlabBlock)
        .build()
    );

    private final Setting<Integer> blocksPer = this.sgGeneral
        .add(
            new IntSetting.Builder()
                .name("每次放置数量")
                .description("每个游戏刻放置的方块数量")
                .defaultValue(1)
                .sliderRange(1, 6)
                .build()
        );
    private final Setting<Integer> delay = this.sgGeneral
        .add(
            new IntSetting.Builder()
                .name("放置延迟")
                .description("放置方块之间的延迟时间（单位：tick）")
                .defaultValue(0)
                .sliderRange(0, 20)
                .build()
        );
    private final Setting<Boolean> render = this.renderGeneral
        .add(
            new BoolSetting.Builder()
                .name("显示渲染")
                .description("是否显示待放置方块的渲染预览")
                .defaultValue(true)
                .build()
        );
    private final Setting<ShapeMode> shapeMode = this.renderGeneral
        .add(
            ((EnumSetting.Builder) ((EnumSetting.Builder) ((EnumSetting.Builder) new EnumSetting.Builder()
                .name("渲染模式"))
                .description("选择渲染的显示模式"))
                .defaultValue(ShapeMode.Both))
                .build()
        );
    private final Setting<SettingColor> readySideColor = this.renderGeneral
        .add(
            new ColorSetting.Builder()
                .name("侧面颜色")
                .description("待放置方块渲染的侧面填充颜色")
                .defaultValue(new SettingColor(255, 255, 255, 50))
                .build()
        );
    private final Setting<SettingColor> readyLineColor = this.renderGeneral
        .add(
            new ColorSetting.Builder()
                .name("线条颜色")
                .description("待放置方块渲染的边框线条颜色")
                .defaultValue(new SettingColor(255, 255, 255, 128))
                .build()
        );
    private List<BlockPos> placeList = new ArrayList<>();
    private int tickCounter = 0;
    int progress = 0;

    public RoadBuilder() {
        super(
            "脚下搭路搭平台",
            "自动在玩家周围建造方块平台或托盘"
        );
    }

    @Override
    public void onActivate() {

    }

    @Override
    public void init() {
        if (this.isActive()) {
            this.toggle();
        }
    }

    @EventHandler
    public void onTick(TickEvent.Pre event) {
        if (!this.moveStop.get()
            || !mc.options.backKey.isPressed()
            && !mc.options.forwardKey.isPressed()
            && !mc.options.leftKey.isPressed()
            && !mc.options.rightKey.isPressed()) {
            this.placeList.clear();
            this.progress = 0;

            // 使用 tick 计时
            if (tickCounter >= this.delay.get()) {
                tickCounter = 0;

                for (BlockPos pos : WorldUtils.getSphere(Float.parseFloat(this.range.get().toString()))) {
                    if (BaritoneUtil.canSeeBlockFace(pos, this.range.get(), true)
                        && this.neighbor(pos)
                        && this.onlyBottom.get()
                        && pos.getY() == mc.player.getBlockPos().down().getY()
                        && mc.world.getBlockState(pos).getBlock().asItem() == Items.AIR) {
                        this.tryPlace(pos);
                    }
                }
            } else {
                tickCounter++;
            }
        }
    }

    @EventHandler
    private void onRender(Render3DEvent event) {
        if (this.render.get()
            && this.placeList.size() > 0
            && (BagUtil.find(this.blockType.get().asItem()).found()
            || (this.blockType.get() instanceof SlabBlock && BagUtil.findClassInventorySlotGrim(SlabBlock.class) != -1)
        )) {
            for (int i = 0; i < this.placeList.size(); i++) {
                double x1 = this.placeList.get(i).getX();
                double y1 = this.placeList.get(i).getY();
                double z1 = this.placeList.get(i).getZ();
                double x2 = this.placeList.get(i).getX() + 1;
                double y2 = this.placeList.get(i).getY() + 1;
                double z2 = this.placeList.get(i).getZ() + 1;
                event.renderer.box(x1, y1, z1, x2, y2, z2, this.readySideColor.get(), this.readyLineColor.get(), this.shapeMode.get(), 0);
            }
        }
    }

    private void tryPlace(BlockPos pos) {
        if (pos != null) {
            if (!this.placeList.contains(pos)) {
                if (this.progress < this.blocksPer.get()) {
                    if (!(MathHelper.sqrt((float) BaritoneUtil.getEyesPos().squaredDistanceTo(pos.toCenterPos())) > this.range.get())) {
                        int itemResult = -1;

                        // 检查是否是半砖
                        if (this.blockType.get() instanceof SlabBlock) {
                            itemResult = BagUtil.findClassInventorySlotGrim(SlabBlock.class);
                        } else {
                            itemResult = BagUtil.findItemInventorySlotGrim(this.blockType.get().asItem());
                        }

                        if (itemResult != -1) {
                            this.placeList.add(pos);
                            BagUtil.doSwap(itemResult);

                            // 根据方块类型和 slapType 设置选择放置方式
                            if (this.blockType.get() instanceof SlabBlock) {
                                if (this.slapType.get() == SlapType.UP) {
                                    BaritoneUtil.placeUpBlock(pos, true, true, true);
                                } else {
                                    BaritoneUtil.placeDownBlock(pos, true, true, true);
                                }
                            } else {
                                BaritoneUtil.placeBlock(pos, true, true, true);
                            }

                            BagUtil.doSwap(itemResult);
                            BagUtil.sync();
                            this.progress++;
                        }
                    }
                }
            }
        }
    }

    private boolean neighbor(BlockPos pos) {
        for (Direction direction : Direction.values()) {
            if (direction != Direction.DOWN && direction != Direction.UP) {
                Block neighborBlock = mc.world.getBlockState(pos.offset(direction)).getBlock();

                // 如果选择的是半砖
                if (this.blockType.get() instanceof SlabBlock) {
                    if (neighborBlock instanceof SlabBlock) {
                        SlabType neighborSlabType = mc.world.getBlockState(pos.offset(direction)).get(Properties.SLAB_TYPE);

                        // 根据 slapType 设置检查邻居半砖类型
                        if (this.slapType.get() == SlapType.UP && neighborSlabType == SlabType.TOP) {
                            return true;
                        } else if (this.slapType.get() == SlapType.DOWN &&
                            (neighborSlabType == SlabType.BOTTOM || neighborSlabType == SlabType.DOUBLE)) {
                            return true;
                        }
                    }
                } else {
                    // 普通方块的邻居检查
                    if (neighborBlock == this.blockType.get()) {
                        return true;
                    }
                }
            }
        }

        return false;
    }


    enum SlapType {

        UP,
        DOWN;
    }

}
